require 'sketchup.rb'

module VRayMaterial
  module_function

  def self.apply_material
    # 调用材质设置脚本
    load File.join(File.dirname(__FILE__), 'vray_material_piliang/vray_material_setup.rb')
    apply_vray_material
  end

  def self.remove_textures
    # 调用材质设置脚本
    load File.join(File.dirname(__FILE__), 'vray_material_piliang/vray_material_setup.rb')
    remove_textures_keep_diffuse
  end

  unless file_loaded?(__FILE__)
    # 创建工具栏
    toolbar = UI::Toolbar.new("VRay材质")

    # 添加第一个按钮 - 应用VRay材质
    cmd1 = UI::Command.new("应用VRay材质") {
      self.apply_material
    }
    cmd1.small_icon = File.join(File.dirname(__FILE__), "vray_material_piliang/vray_material_icon.svg")
    cmd1.large_icon = File.join(File.dirname(__FILE__), "vray_material_piliang/vray_material_icon.svg")
    cmd1.tooltip = "应用VRay材质到选中对象"
    cmd1.status_bar_text = "将VRay材质应用到当前选中的对象"

    # 添加第二个按钮 - 删除贴图
    cmd2 = UI::Command.new("删除贴图") {
      self.remove_textures
    }
    cmd2.small_icon = File.join(File.dirname(__FILE__), "vray_material_piliang/vray_material_remove_icon.svg")
    cmd2.large_icon = File.join(File.dirname(__FILE__), "vray_material_piliang/vray_material_remove_icon.svg")
    cmd2.tooltip = "删除材质中的贴图，只保留漫反射纹理"
    cmd2.status_bar_text = "删除当前材质中的所有贴图，只保留漫反射纹理贴图"

    toolbar.add_item(cmd1)
    toolbar.add_item(cmd2)
    toolbar.show

    file_loaded(__FILE__)
  end
end