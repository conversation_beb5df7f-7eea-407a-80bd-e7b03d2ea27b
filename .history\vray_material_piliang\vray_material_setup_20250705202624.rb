require 'sketchup.rb'

def select_texture_folder
  UI.select_directory(title: "选择贴图文件夹")
end

def classify_textures(folder_path)
  return unless folder_path && File.directory?(folder_path)

  textures = {
    diffuse: nil,
    metalness: nil,
    glossiness: nil,
    bump: nil
  }

  Dir.glob(File.join(folder_path, "*.{png,jpg,jpeg,tif,tiff}")).each do |file|
    filename = File.basename(file).downcase
    if filename.include?("metallic")
      textures[:metalness] = file
    elsif filename.include?("roughness")
      textures[:glossiness] = file
    elsif filename.include?("normal")
      textures[:bump] = file
    else
      textures[:diffuse] = file
    end
  end

  textures
end

scene = VRay::Context.active.scene
scene.change do
  # 获取当前选中材质的名字
  materials = Sketchup.active_model.materials
  current_material = materials.current

  if current_material
    puts "当前选中材质: #{current_material.display_name}"
    material = scene["/#{current_material.display_name}"]
  else
    puts "没有材质被选中"
    return
  end
  if material
    brdf = material[:brdf]
    if brdf
      # 确保使用正确的BRDF类型
      unless brdf.type == :BRDF
        begin
          brdf.type = :BRDF
        rescue NoMethodError
          UI.messagebox("错误: 当前材质无法设置为 VRay 材质类型，请选择支持的材质。")
          return
        end
      end

      # 设置反射颜色
      brdf[:reflect_color] = VRay::Color.new(1, 1, 1)
      brdf[:brdf][:option_use_roughness] = true

      # 选择贴图文件夹并分类贴图
      folder_path = select_texture_folder
      if folder_path
        textures = classify_textures(folder_path)

        # 创建漫反射纹理
        if textures[:diffuse]
          diffuse_tex = scene.create(:TexBitmap, "#{brdf.name}/DiffuseTex") do |tex|
            tex[:bitmap] = scene.create(:BitmapBuffer, "#{tex.name}/BitmapBuffer") do |buffer|
              buffer[:file] = textures[:diffuse]
            end
            tex[:uvwgen] = scene.create(:UVWGenChannel, "#{tex.name}/UVWGenChannel")
          end
          brdf[:diffuse_tex] = diffuse_tex
        end

        # 创建反射光泽度纹理
        if textures[:glossiness]
          glossiness_tex = scene.create(:TexBitmap, "#{brdf.name}/GlossinessTex") do |tex|
            tex[:bitmap] = scene.create(:BitmapBuffer, "#{tex.name}/BitmapBuffer") do |buffer|
              buffer[:file] = textures[:glossiness]
              buffer[:transfer_function] = 0
            end
            tex[:uvwgen] = scene.create(:UVWGenChannel, "#{tex.name}/UVWGenChannel")
          end
          brdf[:reflect_glossiness_tex] = glossiness_tex
        end

        # 创建凹凸贴图
        if textures[:bump]
          bump_tex = scene.create(:TexBitmap, "#{brdf.name}/BumpTex") do |tex|
            tex[:bitmap] = scene.create(:BitmapBuffer, "#{tex.name}/BitmapBuffer") do |buffer|
              buffer[:file] = textures[:bump]
              buffer[:transfer_function] = 0
            end
            tex[:uvwgen] = scene.create(:UVWGenChannel, "#{tex.name}/UVWGenChannel")
          end
          brdf[:bump_map_tex] = bump_tex
        end

        # 创建金属度纹理
        if textures[:metalness]
          metalness_tex = scene.create(:TexBitmap, "#{brdf.name}/MetalnessTex") do |tex|
            tex[:bitmap] = scene.create(:BitmapBuffer, "#{tex.name}/BitmapBuffer") do |buffer|
              buffer[:file] = textures[:metalness]
              buffer[:transfer_function] = 0
            end
            tex[:uvwgen] = scene.create(:UVWGenChannel, "#{tex.name}/UVWGenChannel")
          end
          brdf[:metalness_tex] = metalness_tex
        end
      else
        UI.messagebox("未选择贴图文件夹")
        return
      end
      UI.messagebox("材质设置成功！")

    else
      UI.messagebox("错误: 材质 '#{material.name}' 缺少BRDF属性")
    end
  else
    UI.messagebox("错误: 材质未找到")
  end
end